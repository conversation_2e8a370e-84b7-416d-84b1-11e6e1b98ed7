{% extends "base.html" %}

{% block title %}Phishing Detection - Result{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">🔍 Detection Result</h2>
    </div>
    <div class="card-body p-4">
        <!-- URL being checked -->
        <div class="mb-4">
            <h6 class="fw-bold text-muted">URL Checked:</h6>
            <div class="p-3 bg-light rounded">
                <code class="text-dark">{{ url }}</code>
            </div>
        </div>

        <!-- Result -->
        <div class="result-card">
            {% if confidence_class == 'danger' %}
                <div class="alert phishing-result text-center p-4">
                    <h3 class="mb-2">{{ prediction }}</h3>
                    <p class="mb-0">This URL shows characteristics commonly associated with phishing websites. Exercise caution!</p>
                </div>
                <div class="alert alert-warning">
                    <h6 class="fw-bold">⚠️ Safety Recommendations:</h6>
                    <ul class="mb-0">
                        <li>Do not enter personal information on this website</li>
                        <li>Verify the URL through official channels</li>
                        <li>Check for spelling errors in the domain name</li>
                        <li>Look for secure connection indicators (HTTPS, padlock icon)</li>
                    </ul>
                </div>
            {% else %}
                <div class="alert legitimate-result text-center p-4">
                    <h3 class="mb-2">{{ prediction }}</h3>
                    <p class="mb-0">This URL appears to be legitimate based on our analysis.</p>
                </div>
                <div class="alert alert-info">
                    <h6 class="fw-bold">ℹ️ Note:</h6>
                    <p class="mb-0">While our analysis suggests this URL is legitimate, always remain cautious when entering sensitive information online. Verify the website's authenticity through official channels when in doubt.</p>
                </div>
            {% endif %}
        </div>

        <!-- Action buttons -->
        <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
            <a href="/" class="btn btn-primary">
                🔍 Check Another URL
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="copyResult()">
                📋 Copy Result
            </button>
        </div>

        <!-- Additional info -->
        <div class="mt-4 p-3 bg-light rounded">
            <h6 class="fw-bold mb-2">About this analysis:</h6>
            <p class="mb-2 small">Our machine learning model analyzes various URL characteristics including:</p>
            <ul class="mb-0 small">
                <li>URL length and structure</li>
                <li>Presence of suspicious characters</li>
                <li>Domain characteristics</li>
                <li>Security protocol usage</li>
            </ul>
        </div>
    </div>
</div>

<script>
function copyResult() {
    const resultText = `URL: {{ url }}\nResult: {{ prediction }}\nAnalyzed by Phishing Detection System`;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(resultText).then(function() {
            // Show success message
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '✅ Copied!';
            btn.classList.add('btn-success');
            btn.classList.remove('btn-outline-secondary');
            
            setTimeout(function() {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
            }, 2000);
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = resultText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        alert('Result copied to clipboard!');
    }
}
</script>
{% endblock %}
