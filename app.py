from flask import Flask, render_template, request, jsonify, flash
import pickle
import os
from urllib.parse import urlparse
import re
import logging

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this in production

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variable to store the loaded model
model = None

def load_model():
    """Load the phishing detection model from pickle file"""
    global model
    model_path = 'phishing_model.pkl'
    
    try:
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            logger.info("Model loaded successfully")
            return True
        else:
            logger.error(f"Model file {model_path} not found")
            return False
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return False

def extract_url_features(url):
    """
    Extract features from URL for phishing detection
    Returns a list of features in the order expected by the model
    """
    try:
        # Ensure URL has a scheme
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
        
        parsed_url = urlparse(url)
        
        # Feature 1: Length of URL
        url_length = len(url)
        
        # Feature 2: Number of '@' characters
        at_count = url.count('@')
        
        # Feature 3: Number of '-' characters
        dash_count = url.count('-')
        
        # Feature 4: Whether 'https' is present (1 if yes, 0 if no)
        has_https = 1 if url.startswith('https://') else 0
        
        # Feature 5: Number of dots in the domain
        domain = parsed_url.netloc
        dot_count_in_domain = domain.count('.')
        
        features = [url_length, at_count, dash_count, has_https, dot_count_in_domain]
        
        logger.info(f"Extracted features for URL '{url}': {features}")
        return features
        
    except Exception as e:
        logger.error(f"Error extracting features from URL '{url}': {str(e)}")
        raise

def predict_phishing(url):
    """
    Predict if a URL is phishing or legitimate
    Returns: tuple (prediction_text, confidence_class)
    """
    global model
    
    if model is None:
        raise Exception("Model not loaded")
    
    try:
        # Extract features
        features = extract_url_features(url)
        
        # Make prediction (reshape for single sample)
        prediction = model.predict([features])[0]
        
        # Convert prediction to human-readable format
        if prediction == 1:  # Assuming 1 = phishing, 0 = legitimate
            return "⚠️ Phishing", "danger"
        else:
            return "✅ Legitimate", "success"
            
    except Exception as e:
        logger.error(f"Error making prediction: {str(e)}")
        raise

@app.route('/')
def index():
    """Render the main form page"""
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    """Handle URL prediction request"""
    try:
        # Get URL from form
        url = request.form.get('url', '').strip()
        
        if not url:
            flash('Please enter a URL', 'error')
            return render_template('index.html')
        
        # Basic URL validation
        if not re.match(r'^(https?://)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', url):
            flash('Please enter a valid URL', 'error')
            return render_template('index.html')
        
        # Make prediction
        prediction_text, confidence_class = predict_phishing(url)
        
        return render_template('result.html', 
                             url=url, 
                             prediction=prediction_text,
                             confidence_class=confidence_class)
        
    except Exception as e:
        logger.error(f"Error in prediction route: {str(e)}")
        flash(f'Error processing URL: {str(e)}', 'error')
        return render_template('index.html')

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint for URL prediction"""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({'error': 'URL is required'}), 400
        
        url = data['url'].strip()
        
        if not url:
            return jsonify({'error': 'URL cannot be empty'}), 400
        
        # Make prediction
        prediction_text, confidence_class = predict_phishing(url)
        
        return jsonify({
            'url': url,
            'prediction': prediction_text,
            'confidence_class': confidence_class,
            'features': extract_url_features(url)
        })
        
    except Exception as e:
        logger.error(f"Error in API prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Load the model on startup
    if load_model():
        print("✅ Model loaded successfully. Starting Flask app...")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to load model. Please ensure 'phishing_model.pkl' exists in the project directory.")
        print("The app will not start without a valid model file.")
