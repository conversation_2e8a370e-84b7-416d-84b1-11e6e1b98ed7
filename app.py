# === FLASK PHISHING DETECTION APP ===
# Run this app: python app.py
# Make sure 'phishing_model.pkl' exists in the same directory!

from flask import Flask, render_template, request, jsonify, flash  # Web app tools
import pickle           # To load the saved ML model
import os               # File path handling
from urllib.parse import urlparse  # For breaking down URLs
import re               # Regex for validating URLs
import logging          # Logging info and errors

# === Initialize Flask App ===
app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Used for flash messaging (⚠️ Replace in production)

# === Setup Logging ===
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === Global model variable (to store the loaded ML model) ===
model = None

# === Function: Load the model from a pickle file ===
def load_model():
    global model
    model_path = 'phishing_model.pkl'  # Ensure this file exists!

    try:
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            logger.info("Model loaded successfully")
            return True
        else:
            logger.error(f"Model file {model_path} not found")
            return False
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return False

# === Function: Extract features from a URL ===
# These features are what the ML model uses to predict phishing
def extract_url_features(url):
    try:
        # Add default scheme if missing
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url

        parsed_url = urlparse(url)  # Break the URL into parts

        # Extract simple numeric features
        url_length = len(url)
        at_count = url.count('@')
        dash_count = url.count('-')
        has_https = 1 if url.startswith('https://') else 0
        domain = parsed_url.netloc
        dot_count_in_domain = domain.count('.')

        features = [url_length, at_count, dash_count, has_https, dot_count_in_domain]

        logger.info(f"Extracted features for URL '{url}': {features}")
        return features

    except Exception as e:
        logger.error(f"Error extracting features from URL '{url}': {str(e)}")
        raise

# === Function: Predict if the URL is phishing or legitimate ===
def predict_phishing(url):
    global model

    if model is None:
        raise Exception("Model not loaded")

    try:
        features = extract_url_features(url)
        prediction = model.predict([features])[0]

        if prediction == 1:
            return "⚠️ Phishing", "danger"
        else:
            return "✅ Legitimate", "success"

    except Exception as e:
        logger.error(f"Error making prediction: {str(e)}")
        raise

# === Route: Homepage ===
@app.route('/')
def index():
    return render_template('index.html')  # Render main input page

# === Route: Handle form submission (HTML) ===
@app.route('/predict', methods=['POST'])
def predict():
    try:
        url = request.form.get('url', '').strip()  # Get user input

        if not url:
            flash('Please enter a URL', 'error')
            return render_template('index.html')

        # Basic regex check for valid domain structure
        if not re.match(r'^(https?://)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', url):
            flash('Please enter a valid URL', 'error')
            return render_template('index.html')

        prediction_text, confidence_class = predict_phishing(url)

        return render_template('result.html',
                               url=url,
                               prediction=prediction_text,
                               confidence_class=confidence_class)

    except Exception as e:
        logger.error(f"Error in prediction route: {str(e)}")
        flash(f'Error processing URL: {str(e)}', 'error')
        return render_template('index.html')

# === API Route: Accepts JSON POST requests for programmatic access ===
# Test using: curl -X POST http://localhost:5000/api/predict -H "Content-Type: application/json" -d '{"url":"http://example.com"}'
@app.route('/api/predict', methods=['POST'])
def api_predict():
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({'error': 'URL is required'}), 400

        url = data['url'].strip()

        if not url:
            return jsonify({'error': 'URL cannot be empty'}), 400

        prediction_text, confidence_class = predict_phishing(url)

        return jsonify({
            'url': url,
            'prediction': prediction_text,
            'confidence_class': confidence_class,
            'features': extract_url_features(url)
        })

    except Exception as e:
        logger.error(f"Error in API prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

# === Start the Flask app ===
# Run this script from terminal: python app.py
# Access at: http://localhost:5000/
if __name__ == '__main__':
    if load_model():
        print("✅ Model loaded successfully. Starting Flask app...")
        app.run(debug=True, host='0.0.0.0', port=5000)  # Accessible locally and on network
    else:
        print("❌ Failed to load model. Please ensure 'phishing_model.pkl' exists in the project directory.")
        print("The app will not start without a valid model file.")
